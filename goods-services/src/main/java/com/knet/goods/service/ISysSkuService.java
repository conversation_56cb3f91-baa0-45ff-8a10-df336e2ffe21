package com.knet.goods.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.knet.goods.model.dto.req.SkuQueryRequest;
import com.knet.goods.model.entity.SysSku;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/2/10 13:23
 * @description: SysSku service 服务接口
 */
public interface ISysSkuService extends IService<SysSku> {
    /**
     * 查询sku列表
     *
     * @param request 查询请求
     * @return sku列表
     */
    IPage<SysSku> listSku(SkuQueryRequest request);

    /**
     * 查询商品品牌
     *
     * @return 商品品牌列表
     */
    List<String> queryBrands();

    /**
     * 获取商品名称,图片地址
     *
     * @param sku 商品sku列表
     * @return 商品名称列表
     */
    Map<String, String> getSkuRemarksMap(String sku);

    /**
     * 查询所有不重复的sku_indexed
     *
     * @return 所有不重复的sku_indexed列表
     */
    List<String> selectAllDistinctSkus();

    /**
     * 查询所有不重复的knetProduct remarks
     *
     * @return 所有不重复的remarks列表
     */
    List<String> selectAllDistinctRemarks();
}
