package com.knet.goods.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.goods.mapper.SysSkuMapper;
import com.knet.goods.model.dto.req.SkuQueryRequest;
import com.knet.goods.model.entity.SysSku;
import com.knet.goods.service.ISysSkuService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.knet.common.constants.UserServicesConstants.KNET_SYS_SKU_CACHE_KEY_PREFIX;
import static com.knet.common.constants.UserServicesConstants.KNET_SYS_SKU_EXPIRED_TIME;

/**
 * <AUTHOR>
 * @date 2025/2/19 13:48
 * @description: SysSku service 实现类
 */
@Service
public class SysSkuServiceImpl extends ServiceImpl<SysSkuMapper, SysSku> implements ISysSkuService {

    @Resource
    private SysSkuMapper baseMapper;

    @Override
    public IPage<SysSku> listSku(SkuQueryRequest request) {
        QueryWrapper<SysSku> queryWrapper = new QueryWrapper<>();
        Page<SysSku> page = new Page<>(request.getQueryStartPage(), request.getPageSize());
        page = baseMapper.selectPage(page, queryWrapper);
        IPage<SysSku> userPage = baseMapper.selectPage(page, queryWrapper);
//        return userPage.convert(SysSku::mapToUserInfoDtoResp);
        return userPage;
    }

    @Override
    public List<String> queryBrands() {
        LambdaQueryWrapper<SysSku> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .select(SysSku::getBrand)
                .isNotNull(SysSku::getBrand)
                .groupBy(SysSku::getBrand);
        List<SysSku> sysSkus = baseMapper.selectList(queryWrapper);
        if (CollUtil.isNotEmpty(sysSkus)) {
            return sysSkus.stream()
                    .map(SysSku::getBrand)
                    .filter(StrUtil::isNotBlank)
                    .toList();
        }
        return Collections.emptyList();
    }

    @Override
    public Map<String, String> getSkuRemarksMap(String sku) {
        if (StrUtil.isBlank(sku)) {
            return Collections.emptyMap();
        }
        String redisSysSkuKey = String.format(KNET_SYS_SKU_CACHE_KEY_PREFIX, sku);
        Map<Object, Object> skuMap = RedisCacheUtil.hmget(redisSysSkuKey);
        if (MapUtil.isEmpty(skuMap)) {
            LambdaQueryWrapper<SysSku> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper
                    .select(SysSku::getImg, SysSku::getRemarks)
                    .eq(SysSku::getSku, sku)
                    .isNotNull(SysSku::getImg)
                    .isNotNull(SysSku::getRemarks)
                    .last("LIMIT 1");
            SysSku sysSku = baseMapper.selectOne(queryWrapper);
            if (BeanUtil.isEmpty(sysSku)) {
                return Collections.emptyMap();
            }
            Map<String, Object> cacheMap = new HashMap<>(4);
            String skuImgUrl = StrUtil.emptyToDefault(sysSku.getImg(), "");
            String skuRemarks = StrUtil.emptyToDefault(sysSku.getRemarks(), "");
            cacheMap.put("imgUrl", skuImgUrl);
            cacheMap.put("remarks", skuRemarks);
            RedisCacheUtil.hmset(redisSysSkuKey, cacheMap, KNET_SYS_SKU_EXPIRED_TIME);
            Map<String, String> resultMap = new HashMap<>(2);
            resultMap.put("imgUrl", skuImgUrl);
            resultMap.put("remarks", skuRemarks);
            return resultMap;
        }
        Map<String, String> resultMap = new HashMap<>(2);
        resultMap.put("imgUrl", StrUtil.emptyToDefault((String) skuMap.get("imgUrl"), ""));
        resultMap.put("remarks", StrUtil.emptyToDefault((String) skuMap.get("remarks"), ""));
        return resultMap;
    }

    @Override
    public List<String> selectAllDistinctSkus() {
        return baseMapper.selectAllDistinctSkus();
    }

    @Override
    public List<String> selectAllDistinctRemarks() {
        return baseMapper.selectAllDistinctRemarks();
    }
}
